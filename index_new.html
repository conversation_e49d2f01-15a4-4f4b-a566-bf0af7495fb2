<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>用户体验地图</title>
    <link rel="stylesheet" href="style_new.css">
    <script src="https://cdn.jsdelivr.net/npm/apexcharts"></script>
</head>
<body>
    <div id="journey-map">
        <div class="save-button-container">
            <input type="file" id="loadFile" accept=".json" style="display: none;">
            <button id="loadButton" class="load-button">📂 加载地图</button>
            <button id="saveButton" class="save-button">💾 保存地图</button>
        </div>
        <div class="area area-a">
            <h2>用户模型</h2>
            <div class="user-model-content">
                <div class="user-persona">
                    <h3 class="non-editable">用户</h3>
                    <div class="editable-content" contenteditable="true">
                        <p>描述目标用户的基本信息，包括年龄、职业、技能水平、使用习惯等关键特征...</p>
                    </div>
                </div>
                <div class="scenario">
                    <h3 class="non-editable">场景</h3>
                    <div class="editable-content" contenteditable="true">
                        <p>描述用户使用产品的具体场景，包括时间、地点、环境、触发因素等背景信息...</p>
                    </div>
                </div>
                <div class="goals">
                    <h3 class="non-editable">目标</h3>
                    <div class="editable-content" contenteditable="true">
                        <p>明确用户想要达成的目标，包括功能性需求、情感性需求和期望的结果...</p>
                    </div>
                </div>
            </div>
        </div>
        <div class="area area-b">
            <h2>用户体验过程</h2>
            <div class="journey-stages-container">
                <!-- Stage 1 -->
                <div class="journey-stage" data-stage="1">
                    <div class="stage-header" contenteditable="true">阶段 1: 认知</div>
                    <div class="sentiment-input-container">
                        <label class="sentiment-label">情感值:</label>
                        <input type="number" class="sentiment-input" min="0" max="100" value="60" data-stage="1">
                        <span class="sentiment-indicator">😐</span>
                    </div>
                    <div class="lane actions-lane">
                        <div class="lane-title">用户行为</div>
                        <div class="item" contenteditable="true">用户通过搜索引擎了解产品</div>
                    </div>
                    <div class="lane thoughts-lane">
                        <div class="lane-title">想法 & 感受</div>
                        <div class="item" contenteditable="true">这个产品能解决我的问题吗？</div>
                    </div>
                </div>
                <!-- Stage 2 -->
                <div class="journey-stage" data-stage="2">
                    <div class="stage-header" contenteditable="true">阶段 2: 考虑</div>
                    <div class="sentiment-input-container">
                        <label class="sentiment-label">情感值:</label>
                        <input type="number" class="sentiment-input" min="0" max="100" value="85" data-stage="2">
                        <span class="sentiment-indicator">😊</span>
                    </div>
                    <div class="lane actions-lane">
                        <div class="lane-title">用户行为</div>
                        <div class="item" contenteditable="true">试用产品免费版</div>
                    </div>
                    <div class="lane thoughts-lane">
                        <div class="lane-title">想法 & 感受</div>
                        <div class="item" contenteditable="true">功能看起来不错，但操作有点复杂</div>
                    </div>
                </div>
                <!-- 添加阶段按钮 -->
                <div class="add-stage-button-container">
                    <button class="add-btn">+ 添加阶段</button>
                </div>
            </div>
        </div>

        <!-- Area C: Sentiment Curve -->
        <div class="area area-c">
            <h2>心情曲线</h2>
            <div class="sentiment-curve">
                <div class="chart-description">
                    <p>基于各阶段情感值自动生成的用户心情变化曲线</p>
                </div>
                <div id="chart"></div>
                <!-- 痛点/机会按钮区域 -->
                <div class="chart-opportunities-section" id="chartOpportunitiesSection">
                    <!-- 动态生成的按钮和对应卡片将在这里 -->
                </div>
            </div>
        </div>
    </div>

    <!-- 自定义确认对话框 -->
    <div class="custom-confirm-overlay" id="customConfirm">
        <div class="custom-confirm-dialog">
            <div class="custom-confirm-title">确认删除</div>
            <div class="custom-confirm-message">确定要删除这个用户体验阶段吗？删除后无法恢复。</div>
            <div class="custom-confirm-buttons">
                <button class="custom-confirm-btn cancel" id="confirmCancel">取消</button>
                <button class="custom-confirm-btn confirm" id="confirmDelete">删除</button>
            </div>
        </div>
    </div>

    <script src="script_new.js"></script>
</body>
</html>
