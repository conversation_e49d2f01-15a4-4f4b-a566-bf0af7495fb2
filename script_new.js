document.addEventListener('DOMContentLoaded', () => {
    const stagesContainer = document.querySelector('.journey-stages-container');
    const addStageBtn = document.querySelector('.add-stage-button-container .add-btn');

    // 情感指示器映射
    const sentimentEmojis = {
        0: '😢', 10: '😢', 20: '😟', 30: '😕', 40: '😐',
        50: '😐', 60: '🙂', 70: '😊', 80: '😊', 90: '😄', 100: '😄'
    };

    // 图表配置
    const chartOptions = {
        chart: {
            type: 'area',
            height: 350,
            toolbar: {
                show: false
            },
            zoom: {
                enabled: false
            },
            pan: {
                enabled: false
            },
            animations: {
                enabled: true,
                easing: 'easeinout',
                speed: 800
            }
        },
        series: [{
            name: '情感值',
            data: []
        }],
        xaxis: {
            categories: []
        },
        yaxis: {
            min: 0,
            max: 100,
            title: {
                text: '情感值'
            },
            labels: {
                formatter: function(value) {
                    if (value >= 80) return value + ' 😄';
                    if (value >= 60) return value + ' 😊';
                    if (value >= 40) return value + ' 😐';
                    if (value >= 20) return value + ' 😟';
                    return value + ' 😢';
                }
            }
        },
        stroke: {
            curve: 'smooth',
            width: 3
        },
        fill: {
            type: 'gradient',
            gradient: {
                shadeIntensity: 1,
                opacityFrom: 0.7,
                opacityTo: 0.3,
                stops: [0, 90, 100]
            }
        },
        markers: {
            size: 8,
            colors: ['#0d6efd'],
            strokeColors: '#fff',
            strokeWidth: 2
        },
        grid: {
            borderColor: '#e9ecef',
        },
        tooltip: {
            custom: function({series, seriesIndex, dataPointIndex, w}) {
                const value = series[seriesIndex][dataPointIndex];
                const stage = w.globals.categoryLabels[dataPointIndex];

                // 获取对应阶段的想法和感受
                const stages = document.querySelectorAll('.journey-stage[data-stage]');
                let thoughts = '暂无想法';
                if (stages[dataPointIndex]) {
                    const thoughtsItem = stages[dataPointIndex].querySelector('.thoughts-lane .item');
                    if (thoughtsItem) {
                        thoughts = thoughtsItem.textContent.trim() || '暂无想法';
                    }
                }

                let emoji = '😐';
                if (value >= 80) emoji = '😄';
                else if (value >= 60) emoji = '😊';
                else if (value >= 40) emoji = '😐';
                else if (value >= 20) emoji = '😟';
                else emoji = '😢';

                return `
                    <div style="padding: 16px; background: white; border-radius: 12px; box-shadow: 0 8px 25px rgba(0,0,0,0.15); max-width: 280px;">
                        <div style="font-weight: 700; color: #2d3748; margin-bottom: 8px; font-size: 14px;">${stage}</div>
                        <div style="color: #4a5568; margin-bottom: 8px; font-size: 13px;">情感值: ${value}</div>
                        <div style="font-size: 24px; text-align: center; margin: 8px 0;">${emoji}</div>
                        <div style="border-top: 1px solid #e2e8f0; padding-top: 8px; margin-top: 8px;">
                            <div style="font-weight: 600; color: #667eea; font-size: 12px; margin-bottom: 4px;">想法 & 感受:</div>
                            <div style="color: #4a5568; font-size: 12px; line-height: 1.4; font-style: italic; word-wrap: break-word; overflow-wrap: break-word; word-break: break-all; white-space: pre-wrap; max-width: 248px;">${thoughts}</div>
                        </div>
                    </div>
                `;
            }
        },
        colors: ['#0d6efd']
    };

    const chart = new ApexCharts(document.querySelector("#chart"), chartOptions);
    chart.render();

    // 获取情感指示器
    function getSentimentEmoji(value) {
        const ranges = [0, 10, 20, 30, 40, 50, 60, 70, 80, 90, 100];
        const closestRange = ranges.reduce((prev, curr) => 
            Math.abs(curr - value) < Math.abs(prev - value) ? curr : prev
        );
        return sentimentEmojis[closestRange];
    }

    // 更新情感指示器
    function updateSentimentIndicator(input) {
        const value = parseInt(input.value) || 0;
        const indicator = input.parentElement.querySelector('.sentiment-indicator');
        indicator.textContent = getSentimentEmoji(value);
    }

    // 更新心情曲线
    function updateSentimentChart() {
        const stages = Array.from(document.querySelectorAll('.journey-stage[data-stage]'));
        const sentimentData = [];
        const stageLabels = [];

        stages.forEach(stage => {
            const input = stage.querySelector('.sentiment-input');
            const header = stage.querySelector('.stage-header');
            if (input && header) {
                sentimentData.push(parseInt(input.value) || 0);
                stageLabels.push(header.textContent.trim());
            }
        });

        chart.updateOptions({
            series: [{
                data: sentimentData
            }],
            xaxis: {
                categories: stageLabels
            }
        });
    }

    // 创建新阶段
    function createNewStage() {
        const stageCount = document.querySelectorAll('.journey-stage[data-stage]').length;
        const newStageNumber = stageCount + 1;
        const newStage = document.createElement('div');
        newStage.className = 'journey-stage';
        newStage.setAttribute('data-stage', newStageNumber);
        newStage.innerHTML = `
            <button class="delete-stage-btn">&times;</button>
            <div class="stage-header" contenteditable="true">阶段 ${newStageNumber}</div>
            <div class="sentiment-input-container">
                <label class="sentiment-label">情感值:</label>
                <input type="number" class="sentiment-input" min="0" max="100" value="50" data-stage="${newStageNumber}">
                <span class="sentiment-indicator">😐</span>
            </div>
            <div class="lane actions-lane">
                <div class="lane-title">用户行为</div>
                <div class="item" contenteditable="true">新行为</div>
            </div>
            <div class="lane thoughts-lane">
                <div class="lane-title">想法 & 感受</div>
                <div class="item" contenteditable="true">新想法</div>
            </div>
        `;

        // 添加删除按钮事件 - 添加二次确认
        const deleteBtn = newStage.querySelector('.delete-stage-btn');
        deleteBtn.addEventListener('click', () => {
            showCustomConfirm(() => {
                newStage.remove();
                updateSentimentChart();
            });
        });

        // 添加情感值输入事件
        const sentimentInput = newStage.querySelector('.sentiment-input');
        sentimentInput.addEventListener('input', (e) => {
            updateSentimentIndicator(e.target);
            updateSentimentChart();
        });

        // 添加阶段标题变化事件
        const stageHeader = newStage.querySelector('.stage-header');
        stageHeader.addEventListener('input', updateSentimentChart);

        // 添加想法和感受内容变化事件
        const thoughtsItem = newStage.querySelector('.thoughts-lane .item');
        if (thoughtsItem) {
            thoughtsItem.addEventListener('input', updateSentimentChart);
            thoughtsItem.addEventListener('blur', updateSentimentChart);
        }

        stagesContainer.insertBefore(newStage, addStageBtn.parentElement);

        // 为新阶段添加拖拽功能
        if (window.addDragToStage) {
            window.addDragToStage(newStage);
        }

        updateSentimentChart();

        // 重新生成心情曲线下方的痛点/机会按钮
        updateChartOpportunityButtons();
    }

    // 创建新的阶段
    function createJourneyStage(stageNumber) {
        const newStage = document.createElement('div');
        newStage.className = 'journey-stage';
        newStage.setAttribute('data-stage', stageNumber);
        
        newStage.innerHTML = `
            <button class="delete-stage-btn">&times;</button>
            <div class="stage-header" contenteditable="true">阶段 ${stageNumber}</div>
            <div class="sentiment-input-container">
                <label class="sentiment-label">情感值:</label>
                <input type="number" class="sentiment-input" min="0" max="100" value="50" data-stage="${stageNumber}">
                <span class="sentiment-indicator">😐</span>
            </div>
            <div class="lane actions-lane">
                <div class="lane-title">用户行为</div>
                <div class="item" contenteditable="true">新行为</div>
            </div>
            <div class="lane thoughts-lane">
                <div class="lane-title">想法 & 感受</div>
                <div class="item" contenteditable="true">新想法</div>
            </div>
        `;

        // 添加删除按钮事件
        const deleteBtn = newStage.querySelector('.delete-stage-btn');
        deleteBtn.addEventListener('click', () => {
            showCustomConfirm(() => {
                newStage.remove();
                updateSentimentChart();
            });
        });

        // 添加情感值输入事件
        const sentimentInput = newStage.querySelector('.sentiment-input');
        sentimentInput.addEventListener('input', (e) => {
            updateSentimentIndicator(e.target);
            updateSentimentChart();
        });

        // 添加阶段标题变化事件
        const stageHeader = newStage.querySelector('.stage-header');
        stageHeader.addEventListener('input', updateSentimentChart);

        // 添加想法和感受内容变化事件
        const thoughtsItem = newStage.querySelector('.thoughts-lane .item');
        if (thoughtsItem) {
            thoughtsItem.addEventListener('input', updateSentimentChart);
            thoughtsItem.addEventListener('blur', updateSentimentChart);
        }

        // 为新阶段添加拖拽功能
        if (window.addDragToStage) {
            window.addDragToStage(newStage);
        }

        return newStage;
    }

    // 卡片拖拽排序功能
    function initStageDragAndDrop() {
        const stagesContainer = document.querySelector('.journey-stages-container');
        let draggedStage = null;
        let placeholder = null;

        // 为所有现有阶段添加拖拽功能
        function addDragToStage(stage) {
            stage.draggable = true;

            // 只在点击空白区域时开始拖拽
            stage.addEventListener('mousedown', function(e) {
                // 如果点击的是可编辑内容或输入框，不启动拖拽
                if (e.target.contentEditable === 'true' ||
                    e.target.tagName === 'INPUT' ||
                    e.target.closest('.item') ||
                    e.target.closest('.sentiment-input-container')) {
                    stage.draggable = false;
                    return;
                }
                stage.draggable = true;
            });

            stage.addEventListener('dragstart', function(e) {
                draggedStage = this;
                this.style.opacity = '0.5';

                // 创建占位符
                placeholder = document.createElement('div');
                placeholder.className = 'stage-placeholder';
                placeholder.style.width = this.offsetWidth + 'px';
                placeholder.style.height = this.offsetHeight + 'px';
                placeholder.style.background = 'rgba(102, 126, 234, 0.2)';
                placeholder.style.border = '2px dashed #667eea';
                placeholder.style.borderRadius = '16px';
                placeholder.style.flexShrink = '0';
            });

            stage.addEventListener('dragend', function(e) {
                this.style.opacity = '1';
                if (placeholder && placeholder.parentNode) {
                    placeholder.parentNode.removeChild(placeholder);
                }
                draggedStage = null;
                placeholder = null;
            });
        }

        // 为容器添加拖拽事件
        stagesContainer.addEventListener('dragover', function(e) {
            e.preventDefault();
            if (!draggedStage || !placeholder) return;

            const afterElement = getDragAfterElement(stagesContainer, e.clientX);
            if (afterElement == null) {
                stagesContainer.appendChild(placeholder);
            } else {
                stagesContainer.insertBefore(placeholder, afterElement);
            }
        });

        stagesContainer.addEventListener('drop', function(e) {
            e.preventDefault();
            if (!draggedStage || !placeholder) return;

            // 将拖拽的阶段插入到占位符位置
            stagesContainer.insertBefore(draggedStage, placeholder);
            stagesContainer.removeChild(placeholder);
        });

        // 获取拖拽后应该插入的位置
        function getDragAfterElement(container, x) {
            const draggableElements = [...container.querySelectorAll('.journey-stage:not(.stage-placeholder)')].filter(el => el !== draggedStage);

            return draggableElements.reduce((closest, child) => {
                const box = child.getBoundingClientRect();
                const offset = x - box.left - box.width / 2;

                if (offset < 0 && offset > closest.offset) {
                    return { offset: offset, element: child };
                } else {
                    return closest;
                }
            }, { offset: Number.NEGATIVE_INFINITY }).element;
        }

        // 为所有现有阶段添加拖拽功能
        document.querySelectorAll('.journey-stage').forEach(addDragToStage);

        // 返回函数以便为新创建的阶段添加拖拽功能
        return addDragToStage;
    }

    // 初始化现有阶段的删除按钮
    function initExistingStages() {
        document.querySelectorAll('.journey-stage[data-stage]').forEach(stage => {
            const deleteBtn = document.createElement('button');
            deleteBtn.className = 'delete-stage-btn';
            deleteBtn.innerHTML = '&times;';
            deleteBtn.addEventListener('click', () => {
                showCustomConfirm(() => {
                    stage.remove();
                    updateSentimentChart();
                });
            });
            stage.prepend(deleteBtn);

            // 为现有的情感值输入添加事件监听
            const sentimentInput = stage.querySelector('.sentiment-input');
            if (sentimentInput) {
                sentimentInput.addEventListener('input', (e) => {
                    updateSentimentIndicator(e.target);
                    updateSentimentChart();
                });
                // 初始化情感指示器
                updateSentimentIndicator(sentimentInput);
            }

            // 为阶段标题添加事件监听
            const stageHeader = stage.querySelector('.stage-header');
            if (stageHeader) {
                stageHeader.addEventListener('input', updateSentimentChart);
            }

            // 为想法和感受内容添加事件监听
            const thoughtsItem = stage.querySelector('.thoughts-lane .item');
            if (thoughtsItem) {
                thoughtsItem.addEventListener('input', updateSentimentChart);
                thoughtsItem.addEventListener('blur', updateSentimentChart);
            }
        });
    }

    // 事件监听器
    addStageBtn.addEventListener('click', createNewStage);

    // 初始化用户模型区域的点击清空功能
    function initUserModelClearOnClick() {
        const editableContents = document.querySelectorAll('.user-model-content .editable-content');

        const defaultTexts = {
            'user-persona': '描述目标用户的基本信息，包括年龄、职业、技能水平、使用习惯等关键特征...',
            'scenario': '描述用户使用产品的具体场景，包括时间、地点、环境、触发因素等背景信息...',
            'goals': '明确用户想要达成的目标，包括功能性需求、情感性需求和期望的结果...'
        };

        editableContents.forEach(content => {
            content.addEventListener('click', function() {
                const p = this.querySelector('p');
                const parentClass = this.parentElement.className;
                let defaultText = '';

                if (parentClass.includes('user-persona')) {
                    defaultText = defaultTexts['user-persona'];
                } else if (parentClass.includes('scenario')) {
                    defaultText = defaultTexts['scenario'];
                } else if (parentClass.includes('goals')) {
                    defaultText = defaultTexts['goals'];
                }

                if (p && p.textContent.trim() === defaultText) {
                    p.textContent = '';
                    p.focus();
                }
            });

            // 当失去焦点且内容为空时，恢复提示文字
            content.addEventListener('blur', function() {
                const p = this.querySelector('p');
                if (p && p.textContent.trim() === '') {
                    const parentClass = this.parentElement.className;
                    if (parentClass.includes('user-persona')) {
                        p.textContent = defaultTexts['user-persona'];
                    } else if (parentClass.includes('scenario')) {
                        p.textContent = defaultTexts['scenario'];
                    } else if (parentClass.includes('goals')) {
                        p.textContent = defaultTexts['goals'];
                    }
                }
            });
        });
    }

    // 自定义确认对话框功能
    function showCustomConfirm(onConfirm) {
        const overlay = document.getElementById('customConfirm');
        const confirmBtn = document.getElementById('confirmDelete');
        const cancelBtn = document.getElementById('confirmCancel');

        // 显示对话框
        overlay.classList.add('show');

        // 确认按钮事件
        const handleConfirm = () => {
            overlay.classList.remove('show');
            onConfirm();
            cleanup();
        };

        // 取消按钮事件
        const handleCancel = () => {
            overlay.classList.remove('show');
            cleanup();
        };

        // 点击遮罩层关闭
        const handleOverlayClick = (e) => {
            if (e.target === overlay) {
                overlay.classList.remove('show');
                cleanup();
            }
        };

        // ESC键关闭
        const handleKeyDown = (e) => {
            if (e.key === 'Escape') {
                overlay.classList.remove('show');
                cleanup();
            }
        };

        // 清理事件监听器
        const cleanup = () => {
            confirmBtn.removeEventListener('click', handleConfirm);
            cancelBtn.removeEventListener('click', handleCancel);
            overlay.removeEventListener('click', handleOverlayClick);
            document.removeEventListener('keydown', handleKeyDown);
        };

        // 添加事件监听器
        confirmBtn.addEventListener('click', handleConfirm);
        cancelBtn.addEventListener('click', handleCancel);
        overlay.addEventListener('click', handleOverlayClick);
        document.addEventListener('keydown', handleKeyDown);
    }

    // 保存用户体验地图数据
    document.getElementById('saveButton').addEventListener('click', saveJourneyMap);
    document.getElementById('loadButton').addEventListener('click', () => {
        document.getElementById('loadFile').click();
    });
    document.getElementById('loadFile').addEventListener('change', loadJourneyMap);

    function saveJourneyMap() {
        // 收集用户模型数据
        const journeyData = {
            userModel: {
                persona: document.querySelector('.user-persona .editable-content').innerHTML,
                scenario: document.querySelector('.scenario .editable-content').innerHTML,
                goals: document.querySelector('.goals .editable-content').innerHTML
            },
            stages: []
        };

        // 收集所有阶段数据
        document.querySelectorAll('.journey-stage').forEach(stage => {
            const stageData = {
                title: stage.querySelector('.stage-header').textContent,
                sentimentValue: stage.querySelector('.sentiment-input').value,
                actions: stage.querySelector('.actions-lane .item').innerHTML,
                thoughts: stage.querySelector('.thoughts-lane .item').innerHTML,
                opportunities: []
            };

            // 收集该阶段的机会点数据
            const opportunityCards = stage.querySelectorAll('.opportunity-card');
            opportunityCards.forEach(card => {
                stageData.opportunities.push({
                    content: card.querySelector('.opportunity-content').innerHTML
                });
            });

            journeyData.stages.push(stageData);
        });

        // 转换为JSON字符串并保存
        const jsonString = JSON.stringify(journeyData, null, 2);
        const blob = new Blob([jsonString], { type: 'application/json' });
        const url = URL.createObjectURL(blob);
        
        // 创建下载链接并触发下载
        const a = document.createElement('a');
        a.href = url;
        a.download = `journey-map-${new Date().toISOString().slice(0,10)}.json`;
        document.body.appendChild(a);
        a.click();
        document.body.removeChild(a);
        URL.revokeObjectURL(url);
    }

    function loadJourneyMap(event) {
        const file = event.target.files[0];
        if (!file) return;

        const reader = new FileReader();
        reader.onload = function(e) {
            try {
                const journeyData = JSON.parse(e.target.result);
                
                // 更新用户模型数据
                document.querySelector('.user-persona .editable-content').innerHTML = journeyData.userModel.persona;
                document.querySelector('.scenario .editable-content').innerHTML = journeyData.userModel.scenario;
                document.querySelector('.goals .editable-content').innerHTML = journeyData.userModel.goals;
                
                // 清除现有阶段
                const stagesContainer = document.querySelector('.journey-stages-container');
                const addStageButtonContainer = document.querySelector('.add-stage-button-container');
                Array.from(document.querySelectorAll('.journey-stage')).forEach(stage => stage.remove());
                
                // 添加保存的阶段
                journeyData.stages.forEach((stageData, index) => {
                    const newStage = createJourneyStage(index + 1);
                    
                    // 更新阶段内容
                    newStage.querySelector('.stage-header').textContent = stageData.title;
                    newStage.querySelector('.sentiment-input').value = stageData.sentimentValue;
                    newStage.querySelector('.actions-lane .item').innerHTML = stageData.actions;
                    newStage.querySelector('.thoughts-lane .item').innerHTML = stageData.thoughts;
                    
                    // 在添加按钮之前插入新阶段
                    stagesContainer.insertBefore(newStage, addStageButtonContainer);
                });
                
                // 更新情感曲线
                updateSentimentChart();
                
                // 更新所有阶段的情感指示器
                document.querySelectorAll('.sentiment-input').forEach(input => {
                    updateSentimentIndicator(input);
                });

            } catch (error) {
                console.error('Error loading file:', error);
                alert('加载文件时出错，请确保文件格式正确。\n错误信息: ' + error.message);
            }
        };
        reader.readAsText(file);
    }

    // 初始化现有阶段的保存和加载功能
    function initExistingStages() {
        document.querySelectorAll('.journey-stage[data-stage]').forEach(stage => {
            const deleteBtn = document.createElement('button');
            deleteBtn.className = 'delete-stage-btn';
            deleteBtn.innerHTML = '&times;';
            deleteBtn.addEventListener('click', () => {
                showCustomConfirm(() => {
                    stage.remove();
                    updateSentimentChart();
                });
            });
            stage.prepend(deleteBtn);

            // 为现有的情感值输入添加事件监听
            const sentimentInput = stage.querySelector('.sentiment-input');
            if (sentimentInput) {
                sentimentInput.addEventListener('input', (e) => {
                    updateSentimentIndicator(e.target);
                    updateSentimentChart();
                });
                // 初始化情感指示器
                updateSentimentIndicator(sentimentInput);
            }

            // 为阶段标题添加事件监听
            const stageHeader = stage.querySelector('.stage-header');
            if (stageHeader) {
                stageHeader.addEventListener('input', updateSentimentChart);
            }

            // 为想法和感受内容添加事件监听
            const thoughtsItem = stage.querySelector('.thoughts-lane .item');
            if (thoughtsItem) {
                thoughtsItem.addEventListener('input', updateSentimentChart);
                thoughtsItem.addEventListener('blur', updateSentimentChart);
            }
        });
    }

    // 初始化用户模型区域的点击清空功能
    function initUserModelClearOnClick() {
        const editableContents = document.querySelectorAll('.user-model-content .editable-content');

        const defaultTexts = {
            'user-persona': '描述目标用户的基本信息，包括年龄、职业、技能水平、使用习惯等关键特征...',
            'scenario': '描述用户使用产品的具体场景，包括时间、地点、环境、触发因素等背景信息...',
            'goals': '明确用户想要达成的目标，包括功能性需求、情感性需求和期望的结果...'
        };

        editableContents.forEach(content => {
            content.addEventListener('click', function() {
                const p = this.querySelector('p');
                const parentClass = this.parentElement.className;
                let defaultText = '';

                if (parentClass.includes('user-persona')) {
                    defaultText = defaultTexts['user-persona'];
                } else if (parentClass.includes('scenario')) {
                    defaultText = defaultTexts['scenario'];
                } else if (parentClass.includes('goals')) {
                    defaultText = defaultTexts['goals'];
                }

                if (p && p.textContent.trim() === defaultText) {
                    p.textContent = '';
                    p.focus();
                }
            });

            // 当失去焦点且内容为空时，恢复提示文字
            content.addEventListener('blur', function() {
                const p = this.querySelector('p');
                if (p && p.textContent.trim() === '') {
                    const parentClass = this.parentElement.className;
                    if (parentClass.includes('user-persona')) {
                        p.textContent = defaultTexts['user-persona'];
                    } else if (parentClass.includes('scenario')) {
                        p.textContent = defaultTexts['scenario'];
                    } else if (parentClass.includes('goals')) {
                        p.textContent = defaultTexts['goals'];
                    }
                }
            });
        });
    }

    // 自定义确认对话框功能
    function showCustomConfirm(onConfirm) {
        const overlay = document.getElementById('customConfirm');
        const confirmBtn = document.getElementById('confirmDelete');
        const cancelBtn = document.getElementById('confirmCancel');

        // 显示对话框
        overlay.classList.add('show');

        // 确认按钮事件
        const handleConfirm = () => {
            overlay.classList.remove('show');
            onConfirm();
            cleanup();
        };

        // 取消按钮事件
        const handleCancel = () => {
            overlay.classList.remove('show');
            cleanup();
        };

        // 点击遮罩层关闭
        const handleOverlayClick = (e) => {
            if (e.target === overlay) {
                overlay.classList.remove('show');
                cleanup();
            }
        };

        // ESC键关闭
        const handleKeyDown = (e) => {
            if (e.key === 'Escape') {
                overlay.classList.remove('show');
                cleanup();
            }
        };

        // 清理事件监听器
        const cleanup = () => {
            confirmBtn.removeEventListener('click', handleConfirm);
            cancelBtn.removeEventListener('click', handleCancel);
            overlay.removeEventListener('click', handleOverlayClick);
            document.removeEventListener('keydown', handleKeyDown);
        };

        // 添加事件监听器
        confirmBtn.addEventListener('click', handleConfirm);
        cancelBtn.addEventListener('click', handleCancel);
        overlay.addEventListener('click', handleOverlayClick);
        document.addEventListener('keydown', handleKeyDown);
    }

    // 保存用户体验地图数据
    document.getElementById('saveButton').addEventListener('click', saveJourneyMap);
    document.getElementById('loadButton').addEventListener('click', () => {
        document.getElementById('loadFile').click();
    });
    document.getElementById('loadFile').addEventListener('change', loadJourneyMap);

    function saveJourneyMap() {
        // 收集用户模型数据
        const journeyData = {
            userModel: {
                persona: document.querySelector('.user-persona .editable-content').innerHTML,
                scenario: document.querySelector('.scenario .editable-content').innerHTML,
                goals: document.querySelector('.goals .editable-content').innerHTML
            },
            stages: []
        };

        // 收集所有阶段数据
        document.querySelectorAll('.journey-stage').forEach(stage => {
            const stageData = {
                title: stage.querySelector('.stage-header').textContent,
                sentimentValue: stage.querySelector('.sentiment-input').value,
                actions: stage.querySelector('.actions-lane .item').innerHTML,
                thoughts: stage.querySelector('.thoughts-lane .item').innerHTML,
                opportunities: []
            };

            // 收集该阶段的机会点数据
            const opportunityCards = stage.querySelectorAll('.opportunity-card');
            opportunityCards.forEach(card => {
                stageData.opportunities.push({
                    content: card.querySelector('.opportunity-content').innerHTML
                });
            });

            journeyData.stages.push(stageData);
        });

        // 转换为JSON字符串并保存
        const jsonString = JSON.stringify(journeyData, null, 2);
        const blob = new Blob([jsonString], { type: 'application/json' });
        const url = URL.createObjectURL(blob);
        
        // 创建下载链接并触发下载
        const a = document.createElement('a');
        a.href = url;
        a.download = `journey-map-${new Date().toISOString().slice(0,10)}.json`;
        document.body.appendChild(a);
        a.click();
        document.body.removeChild(a);
        URL.revokeObjectURL(url);
    }

    function loadJourneyMap(event) {
        const file = event.target.files[0];
        if (!file) return;

        const reader = new FileReader();
        reader.onload = function(e) {
            try {
                const journeyData = JSON.parse(e.target.result);
                
                // 更新用户模型数据
                document.querySelector('.user-persona .editable-content').innerHTML = journeyData.userModel.persona;
                document.querySelector('.scenario .editable-content').innerHTML = journeyData.userModel.scenario;
                document.querySelector('.goals .editable-content').innerHTML = journeyData.userModel.goals;
                
                // 清除现有阶段
                const stagesContainer = document.querySelector('.journey-stages-container');
                const addStageButtonContainer = document.querySelector('.add-stage-button-container');
                Array.from(document.querySelectorAll('.journey-stage')).forEach(stage => stage.remove());
                
                // 添加保存的阶段
                journeyData.stages.forEach((stageData, index) => {
                    const newStage = createJourneyStage(index + 1);
                    
                    // 更新阶段内容
                    newStage.querySelector('.stage-header').textContent = stageData.title;
                    newStage.querySelector('.sentiment-input').value = stageData.sentimentValue;
                    newStage.querySelector('.actions-lane .item').innerHTML = stageData.actions;
                    newStage.querySelector('.thoughts-lane .item').innerHTML = stageData.thoughts;
                    
                    // 在添加按钮之前插入新阶段
                    stagesContainer.insertBefore(newStage, addStageButtonContainer);
                });
                
                // 更新情感曲线
                updateSentimentChart();
                
                // 更新所有阶段的情感指示器
                document.querySelectorAll('.sentiment-input').forEach(input => {
                    updateSentimentIndicator(input);
                });

            } catch (error) {
                console.error('Error loading file:', error);
                alert('加载文件时出错，请确保文件格式正确。\n错误信息: ' + error.message);
            }
        };
        reader.readAsText(file);
    }

    // 初始化现有阶段的保存和加载功能
    function initExistingStages() {
        document.querySelectorAll('.journey-stage[data-stage]').forEach(stage => {
            const deleteBtn = document.createElement('button');
            deleteBtn.className = 'delete-stage-btn';
            deleteBtn.innerHTML = '&times;';
            deleteBtn.addEventListener('click', () => {
                showCustomConfirm(() => {
                    stage.remove();
                    updateSentimentChart();
                });
            });
            stage.prepend(deleteBtn);

            // 为现有的情感值输入添加事件监听
            const sentimentInput = stage.querySelector('.sentiment-input');
            if (sentimentInput) {
                sentimentInput.addEventListener('input', (e) => {
                    updateSentimentIndicator(e.target);
                    updateSentimentChart();
                });
                // 初始化情感指示器
                updateSentimentIndicator(sentimentInput);
            }

            // 为阶段标题添加事件监听
            const stageHeader = stage.querySelector('.stage-header');
            if (stageHeader) {
                stageHeader.addEventListener('input', updateSentimentChart);
            }

            // 为想法和感受内容添加事件监听
            const thoughtsItem = stage.querySelector('.thoughts-lane .item');
            if (thoughtsItem) {
                thoughtsItem.addEventListener('input', updateSentimentChart);
                thoughtsItem.addEventListener('blur', updateSentimentChart);
            }
        });
    }

    // 初始化用户模型区域的点击清空功能
    function initUserModelClearOnClick() {
        const editableContents = document.querySelectorAll('.user-model-content .editable-content');

        const defaultTexts = {
            'user-persona': '描述目标用户的基本信息，包括年龄、职业、技能水平、使用习惯等关键特征...',
            'scenario': '描述用户使用产品的具体场景，包括时间、地点、环境、触发因素等背景信息...',
            'goals': '明确用户想要达成的目标，包括功能性需求、情感性需求和期望的结果...'
        };

        editableContents.forEach(content => {
            content.addEventListener('click', function() {
                const p = this.querySelector('p');
                const parentClass = this.parentElement.className;
                let defaultText = '';

                if (parentClass.includes('user-persona')) {
                    defaultText = defaultTexts['user-persona'];
                } else if (parentClass.includes('scenario')) {
                    defaultText = defaultTexts['scenario'];
                } else if (parentClass.includes('goals')) {
                    defaultText = defaultTexts['goals'];
                }

                if (p && p.textContent.trim() === defaultText) {
                    p.textContent = '';
                    p.focus();
                }
            });

            // 当失去焦点且内容为空时，恢复提示文字
            content.addEventListener('blur', function() {
                const p = this.querySelector('p');
                if (p && p.textContent.trim() === '') {
                    const parentClass = this.parentElement.className;
                    if (parentClass.includes('user-persona')) {
                        p.textContent = defaultTexts['user-persona'];
                    } else if (parentClass.includes('scenario')) {
                        p.textContent = defaultTexts['scenario'];
                    } else if (parentClass.includes('goals')) {
                        p.textContent = defaultTexts['goals'];
                    }
                }
            });
        });
    }

    // 自定义确认对话框功能
    function showCustomConfirm(onConfirm) {
        const overlay = document.getElementById('customConfirm');
        const confirmBtn = document.getElementById('confirmDelete');
        const cancelBtn = document.getElementById('confirmCancel');

        // 显示对话框
        overlay.classList.add('show');

        // 确认按钮事件
        const handleConfirm = () => {
            overlay.classList.remove('show');
            onConfirm();
            cleanup();
        };

        // 取消按钮事件
        const handleCancel = () => {
            overlay.classList.remove('show');
            cleanup();
        };

        // 点击遮罩层关闭
        const handleOverlayClick = (e) => {
            if (e.target === overlay) {
                overlay.classList.remove('show');
                cleanup();
            }
        };

        // ESC键关闭
        const handleKeyDown = (e) => {
            if (e.key === 'Escape') {
                overlay.classList.remove('show');
                cleanup();
            }
        };

        // 清理事件监听器
        const cleanup = () => {
            confirmBtn.removeEventListener('click', handleConfirm);
            cancelBtn.removeEventListener('click', handleCancel);
            overlay.removeEventListener('click', handleOverlayClick);
            document.removeEventListener('keydown', handleKeyDown);
        };

        // 添加事件监听器
        confirmBtn.addEventListener('click', handleConfirm);
        cancelBtn.addEventListener('click', handleCancel);
        overlay.addEventListener('click', handleOverlayClick);
        document.addEventListener('keydown', handleKeyDown);
    }

    // 保存用户体验地图数据
    document.getElementById('saveButton').addEventListener('click', saveJourneyMap);
    document.getElementById('loadButton').addEventListener('click', () => {
        document.getElementById('loadFile').click();
    });
    document.getElementById('loadFile').addEventListener('change', loadJourneyMap);

    function saveJourneyMap() {
        // 收集用户模型数据
        const journeyData = {
            userModel: {
                persona: document.querySelector('.user-persona .editable-content').innerHTML,
                scenario: document.querySelector('.scenario .editable-content').innerHTML,
                goals: document.querySelector('.goals .editable-content').innerHTML
            },
            stages: []
        };

        // 收集所有阶段数据
        document.querySelectorAll('.journey-stage').forEach(stage => {
            const stageData = {
                title: stage.querySelector('.stage-header').textContent,
                sentimentValue: stage.querySelector('.sentiment-input').value,
                actions: stage.querySelector('.actions-lane .item').innerHTML,
                thoughts: stage.querySelector('.thoughts-lane .item').innerHTML,
                opportunities: []
            };

            // 收集该阶段的机会点数据
            const opportunityCards = stage.querySelectorAll('.opportunity-card');
            opportunityCards.forEach(card => {
                stageData.opportunities.push({
                    content: card.querySelector('.opportunity-content').innerHTML
                });
            });

            journeyData.stages.push(stageData);
        });

        // 转换为JSON字符串并保存
        const jsonString = JSON.stringify(journeyData, null, 2);
        const blob = new Blob([jsonString], { type: 'application/json' });
        const url = URL.createObjectURL(blob);
        
        // 创建下载链接并触发下载
        const a = document.createElement('a');
        a.href = url;
        a.download = `journey-map-${new Date().toISOString().slice(0,10)}.json`;
        document.body.appendChild(a);
        a.click();
        document.body.removeChild(a);
        URL.revokeObjectURL(url);
    }

    function loadJourneyMap(event) {
        const file = event.target.files[0];
        if (!file) return;

        const reader = new FileReader();
        reader.onload = function(e) {
            try {
                const journeyData = JSON.parse(e.target.result);
                
                // 更新用户模型数据
                document.querySelector('.user-persona .editable-content').innerHTML = journeyData.userModel.persona;
                document.querySelector('.scenario .editable-content').innerHTML = journeyData.userModel.scenario;
                document.querySelector('.goals .editable-content').innerHTML = journeyData.userModel.goals;
                
                // 清除现有阶段
                const stagesContainer = document.querySelector('.journey-stages-container');
                const addStageButtonContainer = document.querySelector('.add-stage-button-container');
                Array.from(document.querySelectorAll('.journey-stage')).forEach(stage => stage.remove());
                
                // 添加保存的阶段
                journeyData.stages.forEach((stageData, index) => {
                    const newStage = createJourneyStage(index + 1);
                    
                    // 更新阶段内容
                    newStage.querySelector('.stage-header').textContent = stageData.title;
                    newStage.querySelector('.sentiment-input').value = stageData.sentimentValue;
                    newStage.querySelector('.actions-lane .item').innerHTML = stageData.actions;
                    newStage.querySelector('.thoughts-lane .item').innerHTML = stageData.thoughts;
                    
                    // 在添加按钮之前插入新阶段
                    stagesContainer.insertBefore(newStage, addStageButtonContainer);
                });
                
                // 更新情感曲线
                updateSentimentChart();
                
                // 更新所有阶段的情感指示器
                document.querySelectorAll('.sentiment-input').forEach(input => {
                    updateSentimentIndicator(input);
                });

            } catch (error) {
                console.error('Error loading file:', error);
                alert('加载文件时出错，请确保文件格式正确。\n错误信息: ' + error.message);
            }
        };
        reader.readAsText(file);
    }

    // 初始化现有阶段的保存和加载功能
    function initExistingStages() {
        document.querySelectorAll('.journey-stage[data-stage]').forEach(stage => {
            const deleteBtn = document.createElement('button');
            deleteBtn.className = 'delete-stage-btn';
            deleteBtn.innerHTML = '&times;';
            deleteBtn.addEventListener('click', () => {
                showCustomConfirm(() => {
                    stage.remove();
                    updateSentimentChart();
                });
            });
            stage.prepend(deleteBtn);

            // 为现有的情感值输入添加事件监听
            const sentimentInput = stage.querySelector('.sentiment-input');
            if (sentimentInput) {
                sentimentInput.addEventListener('input', (e) => {
                    updateSentimentIndicator(e.target);
                    updateSentimentChart();
                });
                // 初始化情感指示器
                updateSentimentIndicator(sentimentInput);
            }

            // 为阶段标题添加事件监听
            const stageHeader = stage.querySelector('.stage-header');
            if (stageHeader) {
                stageHeader.addEventListener('input', updateSentimentChart);
            }

            // 为想法和感受内容添加事件监听
            const thoughtsItem = stage.querySelector('.thoughts-lane .item');
            if (thoughtsItem) {
                thoughtsItem.addEventListener('input', updateSentimentChart);
                thoughtsItem.addEventListener('blur', updateSentimentChart);
            }
        });
    }

    // 初始化用户模型区域的点击清空功能
    function initUserModelClearOnClick() {
        const editableContents = document.querySelectorAll('.user-model-content .editable-content');

        const defaultTexts = {
            'user-persona': '描述目标用户的基本信息，包括年龄、职业、技能水平、使用习惯等关键特征...',
            'scenario': '描述用户使用产品的具体场景，包括时间、地点、环境、触发因素等背景信息...',
            'goals': '明确用户想要达成的目标，包括功能性需求、情感性需求和期望的结果...'
        };

        editableContents.forEach(content => {
            content.addEventListener('click', function() {
                const p = this.querySelector('p');
                const parentClass = this.parentElement.className;
                let defaultText = '';

                if (parentClass.includes('user-persona')) {
                    defaultText = defaultTexts['user-persona'];
                } else if (parentClass.includes('scenario')) {
                    defaultText = defaultTexts['scenario'];
                } else if (parentClass.includes('goals')) {
                    defaultText = defaultTexts['goals'];
                }

                if (p && p.textContent.trim() === defaultText) {
                    p.textContent = '';
                    p.focus();
                }
            });

            // 当失去焦点且内容为空时，恢复提示文字
            content.addEventListener('blur', function() {
                const p = this.querySelector('p');
                if (p && p.textContent.trim() === '') {
                    const parentClass = this.parentElement.className;
                    if (parentClass.includes('user-persona')) {
                        p.textContent = defaultTexts['user-persona'];
                    } else if (parentClass.includes('scenario')) {
                        p.textContent = defaultTexts['scenario'];
                    } else if (parentClass.includes('goals')) {
                        p.textContent = defaultTexts['goals'];
                    }
                }
            });
        });
    }

    // 自定义确认对话框功能
    function showCustomConfirm(onConfirm) {
        const overlay = document.getElementById('customConfirm');
        const confirmBtn = document.getElementById('confirmDelete');
        const cancelBtn = document.getElementById('confirmCancel');

        // 显示对话框
        overlay.classList.add('show');

        // 确认按钮事件
        const handleConfirm = () => {
            overlay.classList.remove('show');
            onConfirm();
            cleanup();
        };

        // 取消按钮事件
        const handleCancel = () => {
            overlay.classList.remove('show');
            cleanup();
        };

        // 点击遮罩层关闭
        const handleOverlayClick = (e) => {
            if (e.target === overlay) {
                overlay.classList.remove('show');
                cleanup();
            }
        };

        // ESC键关闭
        const handleKeyDown = (e) => {
            if (e.key === 'Escape') {
                overlay.classList.remove('show');
                cleanup();
            }
        };

        // 清理事件监听器
        const cleanup = () => {
            confirmBtn.removeEventListener('click', handleConfirm);
            cancelBtn.removeEventListener('click', handleCancel);
            overlay.removeEventListener('click', handleOverlayClick);
            document.removeEventListener('keydown', handleKeyDown);
        };

        // 添加事件监听器
        confirmBtn.addEventListener('click', handleConfirm);
        cancelBtn.addEventListener('click', handleCancel);
        overlay.addEventListener('click', handleOverlayClick);
        document.addEventListener('keydown', handleKeyDown);
    }

    // 保存用户体验地图数据
    document.getElementById('saveButton').addEventListener('click', saveJourneyMap);
    document.getElementById('loadButton').addEventListener('click', () => {
        document.getElementById('loadFile').click();
    });
    document.getElementById('loadFile').addEventListener('change', loadJourneyMap);

    function saveJourneyMap() {
        // 收集用户模型数据
        const journeyData = {
            userModel: {
                persona: document.querySelector('.user-persona .editable-content').innerHTML,
                scenario: document.querySelector('.scenario .editable-content').innerHTML,
                goals: document.querySelector('.goals .editable-content').innerHTML
            },
            stages: []
        };

        // 收集所有阶段数据
        document.querySelectorAll('.journey-stage').forEach(stage => {
            const stageData = {
                title: stage.querySelector('.stage-header').textContent,
                sentimentValue: stage.querySelector('.sentiment-input').value,
                actions: stage.querySelector('.actions-lane .item').innerHTML,
                thoughts: stage.querySelector('.thoughts-lane .item').innerHTML,
                opportunities: []
            };

            // 收集该阶段的机会点数据
            const opportunityCards = stage.querySelectorAll('.opportunity-card');
            opportunityCards.forEach(card => {
                stageData.opportunities.push({
                    content: card.querySelector('.opportunity-content').innerHTML
                });
            });

            journeyData.stages.push(stageData);
        });

        // 转换为JSON字符串并保存
        const jsonString = JSON.stringify(journeyData, null, 2);
        const blob = new Blob([jsonString], { type: 'application/json' });
        const url = URL.createObjectURL(blob);
        
        // 创建下载链接并触发下载
        const a = document.createElement('a');
        a.href = url;
        a.download = `journey-map-${new Date().toISOString().slice(0,10)}.json`;
        document.body.appendChild(a);
        a.click();
        document.body.removeChild(a);
        URL.revokeObjectURL(url);
    }

    function loadJourneyMap(event) {
        const file = event.target.files[0];
        if (!file) return;

        const reader = new FileReader();
        reader.onload = function(e) {
            try {
                const journeyData = JSON.parse(e.target.result);
                
                // 更新用户模型数据
                document.querySelector('.user-persona .editable-content').innerHTML = journeyData.userModel.persona;
                document.querySelector('.scenario .editable-content').innerHTML = journeyData.userModel.scenario;
                document.querySelector('.goals .editable-content').innerHTML = journeyData.userModel.goals;
                
                // 清除现有阶段
                const stagesContainer = document.querySelector('.journey-stages-container');
                const addStageButtonContainer = document.querySelector('.add-stage-button-container');
                Array.from(document.querySelectorAll('.journey-stage')).forEach(stage => stage.remove());
                
                // 添加保存的阶段
                journeyData.stages.forEach((stageData, index) => {
                    const newStage = createJourneyStage(index + 1);
                    
                    // 更新阶段内容
                    newStage.querySelector('.stage-header').textContent = stageData.title;
                    newStage.querySelector('.sentiment-input').value = stageData.sentimentValue;
                    newStage.querySelector('.actions-lane .item').innerHTML = stageData.actions;
                    newStage.querySelector('.thoughts-lane .item').innerHTML = stageData.thoughts;
                    
                    // 在添加按钮之前插入新阶段
                    stagesContainer.insertBefore(newStage, addStageButtonContainer);
                });
                
                // 更新情感曲线
                updateSentimentChart();
                
                // 更新所有阶段的情感指示器
                document.querySelectorAll('.sentiment-input').forEach(input => {
                    updateSentimentIndicator(input);
                });

            } catch (error) {
                console.error('Error loading file:', error);
                alert('加载文件时出错，请确保文件格式正确。\n错误信息: ' + error.message);
            }
        };
        reader.readAsText(file);
    }

    // 初始化现有阶段的保存和加载功能
    function initExistingStages() {
        document.querySelectorAll('.journey-stage[data-stage]').forEach(stage => {
            const deleteBtn = document.createElement('button');
            deleteBtn.className = 'delete-stage-btn';
            deleteBtn.innerHTML = '&times;';
            deleteBtn.addEventListener('click', () => {
                showCustomConfirm(() => {
                    stage.remove();
                    updateSentimentChart();
                });
            });
            stage.prepend(deleteBtn);

            // 为现有的情感值输入添加事件监听
            const sentimentInput = stage.querySelector('.sentiment-input');
            if (sentimentInput) {
                sentimentInput.addEventListener('input', (e) => {
                    updateSentimentIndicator(e.target);
                    updateSentimentChart();
                });
                // 初始化情感指示器
                updateSentimentIndicator(sentimentInput);
            }

            // 为阶段标题添加事件监听
            const stageHeader = stage.querySelector('.stage-header');
            if (stageHeader) {
                stageHeader.addEventListener('input', updateSentimentChart);
            }

            // 为想法和感受内容添加事件监听
            const thoughtsItem = stage.querySelector('.thoughts-lane .item');
            if (thoughtsItem) {
                thoughtsItem.addEventListener('input', updateSentimentChart);
                thoughtsItem.addEventListener('blur', updateSentimentChart);
            }
        });
    }

    // 初始化用户模型区域的点击清空功能
    function initUserModelClearOnClick() {
        const editableContents = document.querySelectorAll('.user-model-content .editable-content');

        const defaultTexts = {
            'user-persona': '描述目标用户的基本信息，包括年龄、职业、技能水平、使用习惯等关键特征...',
            'scenario': '描述用户使用产品的具体场景，包括时间、地点、环境、触发因素等背景信息...',
            'goals': '明确用户想要达成的目标，包括功能性需求、情感性需求和期望的结果...'
        };

        editableContents.forEach(content => {
            content.addEventListener('click', function() {
                const p = this.querySelector('p');
                const parentClass = this.parentElement.className;
                let defaultText = '';

                if (parentClass.includes('user-persona')) {
                    defaultText = defaultTexts['user-persona'];
                } else if (parentClass.includes('scenario')) {
                    defaultText = defaultTexts['scenario'];
                } else if (parentClass.includes('goals')) {
                    defaultText = defaultTexts['goals'];
                }

                if (p && p.textContent.trim() === defaultText) {
                    p.textContent = '';
                    p.focus();
                }
            });

            // 当失去焦点且内容为空时，恢复提示文字
            content.addEventListener('blur', function() {
                const p = this.querySelector('p');
                if (p && p.textContent.trim() === '') {
                    const parentClass = this.parentElement.className;
                    if (parentClass.includes('user-persona')) {
                        p.textContent = defaultTexts['user-persona'];
                    } else if (parentClass.includes('scenario')) {
                        p.textContent = defaultTexts['scenario'];
                    } else if (parentClass.includes('goals')) {
                        p.textContent = defaultTexts['goals'];
                    }
                }
            });
        });
    }

    // 自定义确认对话框功能
    function showCustomConfirm(onConfirm) {
        const overlay = document.getElementById('customConfirm');
        const confirmBtn = document.getElementById('confirmDelete');
        const cancelBtn = document.getElementById('confirmCancel');

        // 显示对话框
        overlay.classList.add('show');

        // 确认按钮事件
        const handleConfirm = () => {
            overlay.classList.remove('show');
            onConfirm();
            cleanup();
        };

        // 取消按钮事件
        const handleCancel = () => {
            overlay.classList.remove('show');
            cleanup();
        };

        // 点击遮罩层关闭
        const handleOverlayClick = (e) => {
            if (e.target === overlay) {
                overlay.classList.remove('show');
                cleanup();
            }
        };

        // ESC键关闭
        const handleKeyDown = (e) => {
            if (e.key === 'Escape') {
                overlay.classList.remove('show');
                cleanup();
            }
        };

        // 清理事件监听器
        const cleanup = () => {
            confirmBtn.removeEventListener('click', handleConfirm);
            cancelBtn.removeEventListener('click', handleCancel);
            overlay.removeEventListener('click', handleOverlayClick);
            document.removeEventListener('keydown', handleKeyDown);
        };

        // 添加事件监听器
        confirmBtn.addEventListener('click', handleConfirm);
        cancelBtn.addEventListener('click', handleCancel);
        overlay.addEventListener('click', handleOverlayClick);
        document.addEventListener('keydown', handleKeyDown);
    }

    // 初始化
    initExistingStages();
    initUserModelClearOnClick();
    window.addDragToStage = initStageDragAndDrop();
    updateSentimentChart();

    // 初始化痛点/机会功能
    initOpportunities();
});

// 心情曲线下方痛点/机会功能
function initOpportunities() {
    generateChartOpportunityButtons();
}

function generateChartOpportunityButtons() {
    const chartOpportunitiesSection = document.getElementById('chartOpportunitiesSection');
    if (!chartOpportunitiesSection) return;

    // 获取所有阶段
    const stages = document.querySelectorAll('.journey-stage[data-stage]');
    const stageCount = stages.length;

    // 创建按钮容器
    const buttonsContainer = document.createElement('div');
    buttonsContainer.className = 'chart-opportunities-buttons';
    buttonsContainer.id = 'chartOpportunityButtons';

    stages.forEach((stage, index) => {
        const stageId = stage.getAttribute('data-stage');
        const stageHeader = stage.querySelector('.stage-header');
        const stageName = stageHeader ? stageHeader.textContent.trim() : `阶段 ${stageId}`;

        // 创建每个阶段的列
        const stageColumn = document.createElement('div');
        stageColumn.className = 'chart-stage-column';
        stageColumn.setAttribute('data-stage', stageId);

        // 根据阶段数量和位置调整对齐方式
        if (stageCount <= 7) {
            if (index === 0) {
                stageColumn.style.alignItems = 'flex-start';
            } else if (index === stageCount - 1) {
                stageColumn.style.alignItems = 'flex-end';
            } else {
                stageColumn.style.alignItems = 'center';
            }
        } else {
            stageColumn.style.alignItems = 'center';
            // 对于超过7个阶段，启用水平滚动
            buttonsContainer.style.justifyContent = 'flex-start';
        }

        // 机会卡片容器
        const opportunitiesContainer = document.createElement('div');
        opportunitiesContainer.className = 'chart-stage-opportunities';
        opportunitiesContainer.setAttribute('data-stage', stageId);

        // 按钮
        const button = document.createElement('button');
        button.className = 'chart-opportunity-btn';
        button.setAttribute('data-stage', stageId);
        button.innerHTML = `
            <span class="btn-icon">💡</span>
            <span class="btn-text">痛点/机会</span>
        `;

        // 添加点击事件
        let opportunityCounter = 1;
        button.addEventListener('click', function() {
            // 重新计算当前阶段的卡片数量，确保编号连续
            const currentCards = opportunitiesContainer.querySelectorAll('.opportunity-card');
            opportunityCounter = currentCards.length + 1;
            addChartOpportunityCard(stageId, opportunityCounter, stageName);
        });

        stageColumn.appendChild(opportunitiesContainer);
        stageColumn.appendChild(button);
        buttonsContainer.appendChild(stageColumn);
    });

    chartOpportunitiesSection.appendChild(buttonsContainer);
}



function addChartOpportunityCard(stageId, counter, stageName) {
    const container = document.querySelector(`.chart-stage-opportunities[data-stage="${stageId}"]`);
    if (!container) return;

    const opportunityCard = document.createElement('div');
    opportunityCard.className = 'opportunity-card';
    opportunityCard.setAttribute('data-stage', stageId);
    opportunityCard.innerHTML = `
        <div class="opportunity-header">
            <div class="opportunity-title">
                <span>💡</span>
                <span>${stageName} - 机会${counter}</span>
            </div>
            <button class="delete-opportunity-btn" onclick="deleteOpportunityCard(this)">×</button>
        </div>
        <div class="opportunity-content" contenteditable="true" placeholder="在这里描述发现的痛点或改进机会...">在这里描述发现的痛点或改进机会...</div>
    `;

    // 添加到对应阶段的容器（新卡片添加到最上面）
    container.insertBefore(opportunityCard, container.firstChild);

    // 为新卡片添加事件监听
    const contentDiv = opportunityCard.querySelector('.opportunity-content');

    // 点击时清空默认文本
    contentDiv.addEventListener('focus', function() {
        if (this.textContent === '在这里描述发现的痛点或改进机会...') {
            this.textContent = '';
        }
    });

    // 失去焦点时如果为空则恢复默认文本
    contentDiv.addEventListener('blur', function() {
        if (this.textContent.trim() === '') {
            this.textContent = '在这里描述发现的痛点或改进机会...';
        }
    });

    // 添加点击事件，点击时置于顶层
    opportunityCard.addEventListener('click', function(e) {
        console.log('Card clicked:', this); // 调试日志
        e.stopPropagation(); // 防止事件冒泡
        bringCardToTop(this);
    });

    // 自动聚焦到新卡片
    setTimeout(() => {
        contentDiv.focus();
    }, 100);
}

function updateChartOpportunityButtons() {
    // 保存现有的痛点卡片数据
    const existingOpportunities = {};
    const existingContainers = document.querySelectorAll('.chart-stage-opportunities');

    existingContainers.forEach(container => {
        const stageId = container.getAttribute('data-stage');
        const cards = container.querySelectorAll('.opportunity-card');
        existingOpportunities[stageId] = [];

        cards.forEach(card => {
            const titleElement = card.querySelector('.opportunity-title span:last-child');
            const contentElement = card.querySelector('.opportunity-content');
            if (titleElement && contentElement) {
                existingOpportunities[stageId].push({
                    title: titleElement.textContent,
                    content: contentElement.textContent
                });
            }
        });
    });

    // 移除现有按钮
    const existingButtons = document.getElementById('chartOpportunityButtons');
    if (existingButtons) {
        existingButtons.remove();
    }

    // 重新生成按钮
    generateChartOpportunityButtons();

    // 恢复保存的痛点卡片数据
    Object.keys(existingOpportunities).forEach(stageId => {
        const opportunities = existingOpportunities[stageId];
        opportunities.forEach(opportunity => {
            restoreOpportunityCard(stageId, opportunity.title, opportunity.content);
        });
    });
}

// 恢复痛点/机会卡片
function restoreOpportunityCard(stageId, title, content) {
    const container = document.querySelector(`.chart-stage-opportunities[data-stage="${stageId}"]`);
    if (!container) return;

    const opportunityCard = document.createElement('div');
    opportunityCard.className = 'opportunity-card';
    opportunityCard.setAttribute('data-stage', stageId);
    opportunityCard.innerHTML = `
        <div class="opportunity-header">
            <div class="opportunity-title">
                <span>💡</span>
                <span>${title}</span>
            </div>
            <button class="delete-opportunity-btn" onclick="deleteOpportunityCard(this)">×</button>
        </div>
        <div class="opportunity-content" contenteditable="true" placeholder="在这里描述发现的痛点或改进机会...">${content}</div>
    `;

    // 添加到对应阶段的容器
    container.insertBefore(opportunityCard, container.firstChild);

    // 为恢复的卡片添加事件监听
    const contentDiv = opportunityCard.querySelector('.opportunity-content');

    // 点击时清空默认文本
    contentDiv.addEventListener('focus', function() {
        if (this.textContent === '在这里描述发现的痛点或改进机会...') {
            this.textContent = '';
        }
    });

    // 失去焦点时如果为空则恢复默认文本
    contentDiv.addEventListener('blur', function() {
        if (this.textContent.trim() === '') {
            this.textContent = '在这里描述发现的痛点或改进机会...';
        }
    });

    // 添加点击事件，点击时置于顶层
    opportunityCard.addEventListener('click', function(e) {
        console.log('Restored card clicked:', this); // 调试日志
        e.stopPropagation(); // 防止事件冒泡
        bringCardToTop(this);
    });
}

// 将卡片置于顶层
function bringCardToTop(clickedCard) {
    // 移除所有卡片的顶层样式并清除位置
    const allCards = document.querySelectorAll('.opportunity-card');
    allCards.forEach(card => {
        if (card !== clickedCard) {
            card.classList.remove('top-layer');
            // 清除保存的位置信息
            card.style.left = '';
            card.style.top = '';
        }
    });

    // 移除所有阶段容器的顶层样式
    const allStages = document.querySelectorAll('.journey-stage');
    allStages.forEach(stage => {
        stage.classList.remove('stage-top-layer');
    });

    // 切换点击卡片的置顶状态
    if (clickedCard.classList.contains('top-layer')) {
        // 如果已经是置顶状态，则取消置顶
        clickedCard.classList.remove('top-layer');
        clickedCard.style.left = '';
        clickedCard.style.top = '';
    } else {
        // 保存当前位置并置顶
        const rect = clickedCard.getBoundingClientRect();
        clickedCard.style.left = rect.left + 'px';
        clickedCard.style.top = rect.top + 'px';
        clickedCard.classList.add('top-layer');

        // 为包含该卡片的阶段容器添加顶层样式
        const parentStage = clickedCard.closest('.journey-stage');
        if (parentStage) {
            parentStage.classList.add('stage-top-layer');
        }
    }
}

// 删除痛点/机会卡片
function deleteOpportunityCard(deleteBtn) {
    const card = deleteBtn.closest('.opportunity-card');

    // 添加删除动画
    card.style.transform = 'translateX(-100%)';
    card.style.opacity = '0';

    setTimeout(() => {
        card.remove();
    }, 300);
}

// 设置全局点击处理，点击其他地方时取消所有卡片的置顶状态
function setupGlobalClickHandler() {
    document.addEventListener('click', function(e) {
        // 如果点击的不是卡片，则取消所有置顶状态
        if (!e.target.closest('.opportunity-card')) {
            const allCards = document.querySelectorAll('.opportunity-card');
            allCards.forEach(card => {
                card.classList.remove('top-layer');
                card.style.left = '';
                card.style.top = '';
            });

            // 移除所有阶段容器的顶层样式
            const allStages = document.querySelectorAll('.journey-stage');
            allStages.forEach(stage => {
                stage.classList.remove('stage-top-layer');
            });
        }
    });
}

// 页面加载完成后初始化
document.addEventListener('DOMContentLoaded', function() {
    setupGlobalClickHandler();
});
